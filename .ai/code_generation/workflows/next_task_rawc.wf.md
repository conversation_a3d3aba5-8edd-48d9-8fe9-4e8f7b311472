---
description: Comprehensive guide for identifying the next task to work on and start working on it, raw-coding mode(no vibe-coding).
---

# Identifying the Next Task to Work On and Start Working On It

This document provides a detailed reference for identifying the next task to work on and start working on it(raw-coding mode, NO vibe-coding). The goal is to identify the next task to work on, to validate all is in place with the current task, and to set the required elements for tracking progress on the next task, locally and on all the integrated third-party platforms (like Github, etc.).

## Workflow Requirements

The workflow for identifying the next task to work on and start working on it requires tools from:

* Desktop Commander MCP Server (<https://desktopcommander.app/>) or Filesystem MCP Server(<https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem>).
* Github MCP Server (<https://github.com/github/github-mcp-server>)
* Git MCP Server (<https://github.com/modelcontextprotocol/servers/tree/main/src/git>).
* Git initialized on the directory (to be located in a git project directory).

## Workflow Steps

### 1. Identify the Current Task

* Scan the project Management Tasks' directory and find the current task file (the one with "status: in-progress" in it).
* Read the current task file and extract its data.
* Show the current task data to the user and ask for confirmation that it is the current task.