---
description: Comprehensive guide for updating the README file.
---

# Updating the README file

This document describes a workflow that define a systematic approach for AI agents to analyze project context and synthesize README content. The workflow ensures consistent, accurate, and professional README maintenance across any repository type, using as a source the previous README  file content, the PRD, the project tasks, and the codebase.

## Workflow Requirements

The workflow for updating the README file requires tools from:

* Desktop Commander MCP Server (<https://desktopcommander.app/>) or Filesystem MCP Server(<https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem>).
* Github MCP Server (<https://github.com/github/github-mcp-server>)
* Git MCP Server (<https://github.com/modelcontextprotocol/servers/tree/main/src/git>)
* Git CLI (<https://cli.github.com/>)
* Git initialized on the directory (to be located in a git project directory).

## Workflow Steps

### Phase 1: Project Context Analysis

#### Step 1.1: Project Type Detection

**Objective**: Identify project category to adapt content appropriately.

**Process**:

1. Analyze git history for contributor patterns.
2. Scan for community files (CONTRIBUTING.md, CODE_OF_CONDUCT.md, issue templates).
3. Check for educational indicators (course references, learning objectives).
4. Identify business context (stakeholder docs, enterprise patterns).

**Output**: Project type classification (Personal, Open Source, Learning, Business).

#### Step 1.2: Documentation Discovery

**Objective**: Catalog all available project documentation.

**Process**:

1. **Primary Documents**: PRD, project outlines, specifications, vision docs.
2. **Technical Documents**: ADRs, design docs, API documentation.
3. **Development Documents**: Issues, tasks, milestones, project boards.
4. **Configuration**: package.json, requirements.txt, Dockerfile, CI configs.
5. **Community Documents**: Contributing guidelines, templates, wikis.

**Output**: Prioritized list of available documentation sources.

#### Step 1.3: Codebase Structure Assessment

**Objective**: Understand current implementation state and technical maturity

**Process**:

1. **Directory Analysis**: Map folder structure and organizational patterns.
2. **Technology Stack**: Identify languages, frameworks, libraries, tools.
3. **Architecture Patterns**: Detect layered architecture, design patterns, conventions.
4. **Maturity Indicators**: Testing setup, CI/CD, documentation coverage, error handling.
5. **Feature Mapping**: Match implemented code to documented requirements.

**Output**: Technical state summary with architecture and maturity assessment.

### Phase 2: Content Synthesis

#### Step 2.1: Header Section Generation

**Objective**: Create compelling project introduction.

**Process**:

1. **Project Name**: Extract from package.json, repo name, or primary documentation.
2. **Tagline**: Synthesize from project vision and unique value proposition.
3. **Badges**: Generate based on actual project setup (build status, coverage, version).
4. **Brief Description**: Combine purpose, approach, and current state (1-2 sentences).

**Output**: Professional header with clear value communication.

#### Step 2.2: Quick Start Section Creation

**Objective**: Provide fastest path to project value.

**Process**:

1. **Installation Method**: Identify simplest setup approach.
2. **Minimal Example**: Create working code snippet or demo.
3. **Success Criteria**: Define what "working" looks like.
4. **Next Steps**: Link to detailed documentation.

**Adaptation Rules**:

* **Libraries**: Installation + basic usage code.
* **Applications**: Setup + demo/screenshot + key features.
* **Learning Projects**: Environment setup + key concept demonstration.
* **Tools**: Installation + common use case example.

**Output**: 5-minute success path with concrete examples.

#### Step 2.3: Core Content Development

**Objective**: Synthesize comprehensive project overview.

**Components**:

**Project Overview**:

* **What**: Core functionality and purpose (from vision docs).
* **Why**: Problem solved and value delivered (from requirements).
* **How**: Technical approach and key decisions (from ADRs + code).
* **Format**: 2-3 paragraphs, scannable, concrete.

**Current Status**:

* **Development Phase**: Current work focus (from recent commits + tasks).
* **Recent Achievements**: Latest completed features/milestones.
* **Next Milestone**: Upcoming objectives and timeline.
* **Stability**: Production readiness and known limitations.

**Architecture & Design**:

* **High-Level Design**: System overview and key components.
* **Technology Stack**: Languages, frameworks, tools with rationale.
* **Key Decisions**: Important architectural choices (from ADRs).
* **Patterns**: Design patterns and conventions used.

**Features & Capabilities**:

* **Core Features**: Primary functionality (from implemented code).
* **Unique Value**: Differentiating capabilities.
* **Technical Highlights**: Impressive technical achievements.
* **Integration Points**: External services and APIs.

**Output**: Comprehensive yet concise project context.

#### Step 2.4: Project-Type Specific Sections

**Objective**: Add relevant sections based on project type.

**Personal Projects**:

* Learning objectives and skills demonstrated.
* Technical challenges solved.
* Evolution and growth story.
* Portfolio positioning.

**Open Source Projects**:

* Community value proposition.
* Contribution guidelines and process.
* Roadmap and community involvement.
* Governance and maintenance.

**Learning/Educational Projects**:

* Educational goals and progression.
* Concepts demonstrated and mastered.
* Learning outcomes and milestones.
* Knowledge transfer value.

**Business Projects**:

* Business value and ROI.
* Stakeholder benefits.
* Production deployment info.
* Compliance and security considerations.

**Output**: Tailored content sections for intended audience.

### Phase 3: Quality Assurance

#### Step 3.1: Accuracy Verification

**Objective**: Ensure all information reflects actual project state.

**Verification Points**:

* [ ] Technology stack matches actual dependencies.
* [ ] Features described exist in codebase.
* [ ] Installation instructions are functional.
* [ ] Code examples work with current version.
* [ ] Links are valid and current.
* [ ] Badges reflect actual status.
* [ ] Timeline information is current.

**Process**: Cross-reference README content with actual project artifacts.

#### Step 3.2: Quality Standards Check

**Objective**: Ensure professional presentation and usability.

**Quality Criteria**:

* [ ] Header communicates value within 10 seconds.
* [ ] Quick Start enables success within 5 minutes.
* [ ] Content is scannable with clear headings.
* [ ] Technical depth without overwhelming newcomers.
* [ ] Professional tone suitable for portfolio/sharing.
* [ ] Clear next steps for different user types.
* [ ] Appropriate length and detail level.

**Process**: Review against universal quality standards.

#### Step 3.3: Audience Adaptation Verification

**Objective**: Confirm content serves intended audience appropriately.

**Adaptation Checks**:

* [ ] Tone matches project type (personal/open/learning/business).
* [ ] Technical level appropriate for intended users.
* [ ] Contributing information present for open projects.
* [ ] Learning outcomes highlighted for educational projects.
* [ ] Business value clear for professional projects.
* [ ] Community elements present for collaborative projects.

**Process**: Validate audience-specific adaptations.

### Phase 4: Output Generation

#### Step 4.1: README Structure Assembly

**Objective**: Organize synthesized content into final README structure.

**Standard Structure**:

1. Header (name, tagline, badges, brief description).
2. Project Overview (what, why, how).
3. Quick Start (fastest success path).
4. Current Status (phase, recent work, next steps).
5. Architecture & Design (design, stack, decisions).
6. Features & Capabilities (functionality, value).
7. Installation & Usage (detailed setup and examples).
8. Development (local setup, testing, workflow).
9. Documentation (references, guides, API docs).
10. Contributing (for open projects).
11. Roadmap & Progress (milestones, plans).
12. Learning Outcomes (for educational projects).
13. License & Acknowledgments.

**Process**: Assemble sections in logical flow with appropriate depth.

#### Step 4.2: Final Formatting and Polish

**Objective**: Ensure professional presentation and readability.

**Formatting Requirements**:

* Consistent heading hierarchy.
* Proper markdown syntax.
* Working links and references.
* Appropriate emoji usage for scannability.
* Code blocks with proper language tags.
* Tables for complex information.
* Collapsible sections for optional detail.

**Process**: Apply consistent formatting and visual hierarchy.

## Workflow Execution Notes

### Information Source Priority

1. **Most Recent**: Git commits, current branch, active tasks.
2. **Authoritative**: Official project documentation, ADRs.
3. **Implementation Truth**: Actual codebase and configuration.
4. **Planning**: Roadmaps, issue backlogs, project boards.

### Synthesis Principles

* **Accuracy First**: Never invent information not present in sources.
* **Synthesis Over Duplication**: Combine and distill, don't copy.
* **Currency**: Prioritize recent and current information.
* **Relevance**: Focus on information valuable to intended audience.
* **Clarity**: Prefer concrete examples over abstract descriptions.

### Quality Gates

* **Technical Accuracy**: All technical claims verifiable in codebase.
* **Functional Validity**: All instructions and examples work.
* **Professional Standard**: Suitable for portfolio or business presentation.
* **Audience Appropriateness**: Content serves intended users effectively.

### Error Handling

* **Missing Information**: Clearly indicate when information is unavailable.
* **Conflicting Sources**: Prioritize most recent and authoritative.
* **Incomplete Implementation**: Accurately represent current state vs planned.
* **Uncertainty**: Use appropriate qualifiers rather than definitive claims.
